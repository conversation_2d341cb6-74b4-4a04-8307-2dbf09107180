import { Plugin, showMessage, openTab, <PERSON>u, getFrontend } from "siyuan";
import "@/styles/components.scss";
// @ts-ignore
import Player from "./components/Player.svelte";
// @ts-ignore
import PlayList from "./components/PlayList.svelte";
// @ts-ignore
import Setting from "./components/Setting.svelte";
// @ts-ignore
import Assistant from "./components/Assistant.svelte";
// @ts-ignore
import Notes from "./components/Notes.svelte";

import type { ComponentInstance } from './core/types';

export default class MediaPlayerPlugin extends Plugin {
    private components = new Map<string, ComponentInstance>();
    private events = new Map<string, EventListener>();
    private linkClickHandler: ((e: MouseEvent) => Promise<void>) | null = null;
    private tabInstance: any = null;
    public playerAPI: any;
    private isMobile = () => getFrontend().endsWith('mobile'); // 运行环境判断

    /** 保存数据，支持格式化JSON */
    async saveData(f: string, d: any, i?: number) { return super.saveData(f, i !== undefined ? JSON.stringify(d, null, i) : d); }

    /** 获取插件配置 */
    private async getConfig() {
        const d = await this.loadData('config.json');
        return d && typeof d === 'object' && !Array.isArray(d) ? { settings: {}, ...d } : { settings: {} };
    }

    /** 插件加载初始化 */
    async onload() {
        await this.initAPI();
        this.registerEvents();
        this.addUI();
    }

    /** 插件卸载清理 */
    onunload() {
        this.events.forEach((handler, event) =>
            (event === 'linkClick' ? document : window).removeEventListener(event === 'linkClick' ? 'click' : event, handler, event === 'linkClick')
        );
        this.components.forEach(c => c?.$destroy?.());
        this.components.clear();
        this.events.clear();
        this.tabInstance = this.linkClickHandler = this.playerAPI = null;
        (window as any).siyuanMediaPlayer = null;
    }

    /** 初始化API和链接处理器 */
    private async initAPI() {
        const { createLinkClickHandler } = await import('./core/player');

        const player = () => this.components.get('player');
        this.playerAPI = {
            getCurrentTime: () => player()?.getCurrentTime?.() || 0,
            seekTo: (time: number) => player()?.seekTo?.(time),
            getCurrentMedia: () => player()?.getCurrentMedia?.() || null,
            play: (url: string, options: any) => player()?.play?.(url, options),
            createTimestampLink: async (withScreenshot: boolean, startTime: number, endTime?: number, subtitle?: string) => {
                const { link } = await import('./core/document');
                const currentItem = this.playerAPI.getCurrentMedia();
                return currentItem ? await link(currentItem, await this.getConfig(), startTime, endTime, subtitle) : '';
            }
        };

        this.linkClickHandler = createLinkClickHandler(
            this.playerAPI,
            () => this.getConfig(),
            () => this.openTab(),
            (item: any, startTime?: number, endTime?: number) => this.components.get('playlist')?.play?.(item, startTime, endTime)
        );
        document.addEventListener('click', this.linkClickHandler, true);
        this.events.set('linkClick', this.linkClickHandler);
    }

    /** 注册事件监听器和快捷键 */
    private registerEvents() {
        const handlers = {
            'siyuanMediaPlayerUpdate': (e: CustomEvent<any>) => {
                const { currentItem } = e.detail;
                if (this.tabInstance?.parent?.updateTitle && currentItem?.title) {
                    try { this.tabInstance.parent.updateTitle(currentItem.title); } catch {}
                }
                const assistant = this.components.get('assistant');
                if (assistant?.$set) assistant.$set({ currentMedia: currentItem });
            },
            'updatePlayerConfig': (e: CustomEvent) => {
                this.playerAPI?.updateConfig?.(e.detail);
            },
            'playMediaItem': async (e: CustomEvent) => {
                const { play } = await import('./core/player');
                await play(e.detail, this.playerAPI, await this.getConfig(), (item) =>
                    window.dispatchEvent(new CustomEvent('siyuanMediaPlayerUpdate', { detail: { currentItem: item } })));
            },
            'mediaPlayerTabChange': async (e: CustomEvent) => {
                const tabId = e.detail?.tabId;
                if (tabId) {
                    const container = document.querySelector('.media-player-sidebar-content');
                    if (container) await this.showTabContent(tabId, container as HTMLElement);
                }
            },
            'mediaPlayerAction': async (e: CustomEvent) => {
                const { action, loopStartTime } = e.detail;
                const currentItem = this.playerAPI?.getCurrentMedia?.();
                const config = await this.getConfig();

                if (!currentItem) return;

                try {
                    const { player, mediaNotes } = await import('./core/document');

                    switch (action) {
                        case 'screenshot':
                            await player.screenshot(this.playerAPI, currentItem, config, this.i18n);
                            break;
                        case 'timestamp':
                            await player.timestamp(this.playerAPI, currentItem, config, this.i18n);
                            break;
                        case 'loopSegment':
                            // loopStartTime存在说明是第二次点击，创建循环链接
                            if (loopStartTime !== undefined) {
                                await player.loop(this.playerAPI, currentItem, config, this.i18n, loopStartTime);
                            }
                            break;
                        case 'mediaNotes':
                            await mediaNotes.create(currentItem, config, this.playerAPI, this.i18n, this);
                            break;
                    }
                } catch (error) {
                    console.error(`执行${action}失败:`, error);
                    showMessage(`操作失败: ${error.message || error}`);
                }
            },
            'mediaEnded': () => this.components.get('playlist')?.playNext?.()
        };

        Object.entries(handlers).forEach(([event, handler]) => {
            const listener = handler.bind(this) as EventListener;
            this.events.set(event, listener);
            window.addEventListener(event, listener);
        });

        this.addHotkeys();
    }

    /** 添加侧边栏和顶栏UI */
    private addUI() {
        const iconId = 'siyuan-media-player-icon';
        this.addIcons(`
            <symbol id="${iconId}" viewBox="0 0 1024 1024">
                <path d="M753.265 105.112c12.57 12.546 12.696 32.81 0.377 45.512l-0.377 0.383-73.131 72.992L816 224c70.692 0 128 57.308 128 128v448c0 70.692-57.308 128-128 128H208c-70.692 0-128-57.308-128-128V352c0-70.692 57.308-128 128-128l136.078-0.001-73.13-72.992c-12.698-12.674-12.698-33.222 0-45.895 12.697-12.674 33.284-12.674 45.982 0l119.113 118.887h152.126l119.114-118.887c12.697-12.674 33.284-12.674 45.982 0zM457 440c-28.079 0-51 22.938-51 51v170c0 9.107 2.556 18.277 7 26 15.025 24.487 46.501 32.241 71 18l138-84c7.244-4.512 13.094-10.313 17-17 15.213-24.307 7.75-55.875-16-71l-139-85c-7.994-5.355-17.305-8-27-8z"/>
            </symbol>
            <symbol id="iconDragon" viewBox="0 0 1024 1024">
                <path d="M680.59136 95.08864c7.168 8.68352 44.52352 61.8496 70.67648 91.87328 1.18784-17.87904 2.43712-34.95936 11.65312-50.176-2.41664 33.50528 25.92768 73.728 36.72064 86.85568 17.46944 23.51104 45.91616 23.20384 41.5744-6.47168 23.8592 19.10784 25.64096 51.87584 2.47808 64.55296 25.92768 28.3648 24.24832 31.49824 37.94944 35.55328 8.41728 2.21184 12.82048 0 16.6912-2.84672 2.23232-1.65888 2.72384-6.84032-3.2768-10.73152-29.81888-19.29216-14.336-59.1872-12.94336-59.392 0 0.98304-4.46464 32.11264 12.12416 42.16832 38.912 22.99904 41.30816 41.5744 37.13024 73.97376-1.536 10.24-15.95392 29.45024-34.816 37.60128a59.74016 59.74016 0 0 0 4.44416-18.80064c-5.18144 7.76192-24.39168 17.59232-38.68672 17.46944a86.016 86.016 0 0 0 15.52384-17.46944c-3.35872-0.96256-6.59456 1.04448-8.76544 3.80928-2.41664 3.13344-14.58176 15.44192-37.13024 9.50272 8.45824-3.9936 9.85088-7.39328 11.59168-12.288 1.10592-3.1744 0.47104-4.34176-2.84672-4.97664-43.49952-7.18848-52.65408-41.14432-94.208-62.81216a122.55232 122.55232 0 0 0-14.336-5.38624c-1.90464-0.69632-3.13344 0-3.42016 2.048-1.3312 8.94976-5.61152 31.51872 16.6912 40.38656 14.00832 7.20896 50.09408 11.89888 64.43008 49.152-25.74336-16.15872-30.72-14.70464-41.5744-14.49984 16.384 11.50976 23.51104 19.98848 28.95872 30.72 5.87776 13.02528 14.04928 14.336 22.79424 15.60576 16.6912 4.32128 22.528-11.07968 23.22432-11.8784 9.87136 28.81536-3.3792 45.6704-22.528 51.97824a138.26048 138.26048 0 0 1-41.53344 6.00064c-42.78272 2.41664-44.27776-44.05248-92.75392-35.38944 6.51264-5.8368 23.7568-21.77024 47.616-5.16096-3.01056-5.87776-3.29728-13.33248-28.24192-19.27168 27.62752-8.66304 45.24032 4.38272 56.4224 9.03168 0.24576-0.2048-1.39264-26.05056-72.6016-43.008-16.896-3.31776-106.63936-19.27168-105.00096-83.41504 14.336 28.83584 52.40832 35.55328 69.632 14.58176 2.048-2.53952 1.6384-3.8912-1.4336-5.89824-69.34528-48.66048-292.31104 14.78656-231.95648 99.84-6.73792-0.57344-91.19744 4.77184-98.18112-89.92768-7.76192 68.32128 44.6464 99.38944 62.09536 107.78624 28.44672 20.48 361.34912 43.64288 453.36576 86.7328 27.29984 10.5472 31.744 33.09568 31.76448 56.13568 0.16384 189.68576-157.16352 301.54752-294.62528 384.2048a3.82976 3.82976 0 0 1-3.7888 0c-104.71424-72.62208-299.47904-152.24832-289.01376-405.83168C325.632 719.19616 551.66976 813.056 563.44576 815.8208c20.70528-8.192 109.34272-44.3392 66.19136-112.35328 17.53088 6.8608 30.0032 18.92352 40.57088 33.54624 10.24-26.39872 8.8064-50.56512-12.53376-71.35232 40.61184 5.75488 75.18208 22.528 103.09632 53.6576-65.536-145.408-316.3136-76.0832-396.36992-135.168-78.58176-58.51136-81.7152-137.216-132.64896-184.5248 14.336 2.2528 28.2624 4.97664 40.77568 12.92288-7.70048-24.576-23.3472-41.96352-53.6576-64.9216 12.67712-7.12704 27.91424-9.68704 42.82368-9.66656-14.56128-10.56768-36.18816-27.70944-47.104-46.73536 15.9744 6.51264 41.8816 1.16736 49.90976-3.93216 41.79968-35.79904 82.47296-69.07904 126.976-92.16 79.52384-41.1648 164.00384-32.17408 170.0864-47.47264 0.57344-0.6144 0.32768-12.8-0.96256-17.69472-1.18784-8.02816-20.48-17.85856-37.92896-26.8288 23.12192 1.26976 83.10784 1.35168 132.11648 47.26784 11.42784-37.39648-34.44736-91.32032-69.34528-96.256 21.504-0.02048 72.43776 9.05216 95.15008 40.93952z m86.54848 162.07872c7.24992 22.65088 13.2096 34.816 23.61344 39.38304 9.8304 4.096 21.1968 1.24928 25.92768-7.24992 1.04448-1.88416 0.57344-2.72384-1.65888-3.60448a65.86368 65.86368 0 0 1-13.2096-6.59456c-8.99072-5.75488-18.20672-17.1008-34.67264-21.93408z"/>
                <path d="M270.92992 650.5472c-5.24288-20.48-7.61856-56.05376-5.87776-70.30784 7.33184-133.632-90.5216-135.82336-123.57632-171.52-49.152-55.05024-47.104-113.60256-36.7616-154.5216-36.2496 50.91328-18.432 156.3648-8.64256 170.68032a8.704 8.704 0 0 0 6.90176 5.30432c9.216 4.58752 38.912 23.36768 58.40896 53.1456-27.70944-1.00352-49.39776-12.53376-66.88768-32.768 1.16736 7.4752 25.2928 47.80032 35.71712 60.60032 2.9696 4.096 4.54656 6.144 12.86144 8.192 63.1808 22.44608 127.85664 131.19488 127.85664 131.19488z"/>
            </symbol>
            <symbol id="iconWebDAV" viewBox="0 0 1462 1024">
                <path d="M1216.585143 282.697143c42.057143-37.814857 97.865143-57.782857 132.461714-41.325714 28.16 18.432 46.299429 48.64 49.298286 81.188571a32.768 32.768 0 0 1-13.385143 32.987429 34.889143 34.889143 0 0 1-35.401143 0.731428 115.126857 115.126857 0 0 0-20.845714-5.851428 58.148571 58.148571 0 0 0-48.420572 12.214857 430.08 430.08 0 0 1 68.534858 263.314285 370.322286 370.322286 0 0 1-25.892572 123.245715c-27.574857 70.070857-68.973714 112.274286-121.856 91.794285-5.12 5.485714-10.825143 10.166857-16.530286 13.677715a534.089143 534.089143 0 0 1-309.613714 117.906285h-36.571428c-172.032 0-281.234286-72.704-298.788572-145.846857a246.345143 246.345143 0 0 1-4.461714-24.722285c-15.140571-108.251429 3.072-240.347429 80.676571-378.733715a530.432 530.432 0 0 1 140.141715-160.548571c2.779429-1.974857 5.632-3.803429 8.484571-5.632a70.509714 70.509714 0 0 1 2.633143-39.350857c6.875429-13.238857 17.334857-24.283429 30.427428-32.036572 19.968-10.752 41.472-18.212571 63.853715-22.162285h0.438857a433.664 433.664 0 0 1 354.816 119.222857z m-373.540572-174.811429a26.331429 26.331429 0 0 1 9.947429 31.817143l-2.706286 5.12a26.916571 26.916571 0 0 1-31.451428 9.874286l-5.266286-2.706286A210.212571 210.212571 0 0 0 495.030857 264.777143a26.916571 26.916571 0 0 1-25.6 18.944l-5.412571-0.512a146.285714 146.285714 0 0 0-27.355429-2.779429 137.069714 137.069714 0 0 0-134.363428 123.611429l-0.731429 11.337143a65.901714 65.901714 0 0 0 0 11.117714 27.062857 27.062857 0 0 1-13.604571 25.965714l-5.924572 2.413715a218.697143 218.697143 0 0 0-163.181714 212.48 215.332571 215.332571 0 0 0 63.049143 154.697142 221.915429 221.915429 0 0 0 155.940571 64.219429h190.902857a26.331429 26.331429 0 1 1 0 52.662857h-190.902857a273.115429 273.115429 0 0 1-193.682286-79.433143 271.652571 271.652571 0 0 1 102.692572-449.316571A188.269714 188.269714 0 0 1 421.302857 227.181714l14.555429-0.512h14.628571a267.264 267.264 0 0 1 392.557714-118.784z m0 0z m-32.841142 187.977143a47.323429 47.323429 0 0 0-22.601143 10.459429A477.476571 477.476571 0 0 0 662.089143 449.828571c-71.314286 126.683429-88.502857 246.564571-75.629714 343.552 1.316571 9.801143 2.56 16.457143 3.291428 19.456 12.8 43.885714 110.811429 102.985143 247.222857 105.252572h36.059429a480.182857 480.182857 0 0 0 276.406857-105.179429l3.437714-2.706285c17.627429-12.946286 20.772571-23.186286 11.702857-52.809143a698.587429 698.587429 0 0 0-136.265142-244.589715 1624.429714 1624.429714 0 0 0-111.616-127.268571 764.635429 764.635429 0 0 0-103.424-88.502857 9.874286 9.874286 0 0 0-3.072-1.170286z m162.011428-73.654857a377.490286 377.490286 0 0 0-95.085714-2.121143 102.107429 102.107429 0 0 0-57.197714 20.187429l-0.219429 0.438857c-0.146286 0.292571-0.146286 0.731429 0 1.609143l1.901714 0.219428c8.850286 1.462857 17.334857 5.12 24.576 10.313143 39.936 28.16 77.092571 59.977143 111.030857 95.085714a1622.674286 1622.674286 0 0 1 113.664 131.291429 748.544 748.544 0 0 1 148.114286 264.338286c4.754286 15.725714 6.363429 32.182857 4.608 48.493714 10.825143 1.609143 21.723429-9.801143 32.475429-32.914286 2.413714-5.12 4.681143-10.678857 7.168-17.188571l5.339428-13.897143c12.8-34.377143 20.260571-70.363429 22.089143-107.081143a378.806857 378.806857 0 0 0-60.562286-232.448 134.802286 134.802286 0 0 0-12.214857-17.408 385.755429 385.755429 0 0 0-245.686857-148.918857z m0 0h0.073143z m351.232 63.853714c-27.574857 1.901714-53.540571 13.165714-73.728 31.817143l1.024 1.170286c22.601143-19.748571 52.955429-28.306286 82.578286-23.04a100.425143 100.425143 0 0 0-9.874286-9.947429z m21.430857 14.336l1.462857 2.048a5.412571 5.412571 0 0 0-3.876571 2.779429c-0.073143 0.146286-0.219429 0.073143-0.512-0.146286l1.682286-5.12 1.243428 0.438857z m3.876572 2.048z"/>
            </symbol>
            <symbol id="iconOpenList" viewBox="0 0 1024 1024">
                <path d="M436.95 120.74C469.18 115.44 502.20 116.04 534.71 118.11C564.38 120.66 593.51 127.62 622.06 135.79C622.85 136.32 623.65 136.86 624.45 137.41C627.56 137.78 630.53 138.81 633.53 139.68C634.09 140.25 634.67 140.83 635.25 141.40C661.10 149.60 685.08 162.84 708.54 176.28C732.39 191.04 754.15 208.96 774.69 228.00C779.24 232.90 784.25 237.33 788.88 242.15C794.21 250.31 802.43 256.19 807.48 264.53C826.56 287.61 842.19 313.36 854.90 340.47C856.48 344.15 858.69 347.58 859.76 351.48C861.63 356.51 865.34 361.14 865.51 366.60C866.15 367.31 866.80 368.01 867.46 368.72C867.44 369.72 867.44 370.73 867.44 371.73C867.91 372.10 868.87 372.84 869.34 373.21C869.39 374.26 869.44 375.32 869.50 376.38C879.53 401.69 884.94 428.97 889.37 455.87C896.46 515.13 891.02 575.97 871.75 632.52C870.07 639.40 867.05 645.86 865.01 652.64C864.65 653.06 863.94 653.92 863.58 654.34C863.18 658.01 861.39 661.28 859.74 664.51C858.15 670.12 854.57 674.90 852.92 680.49C847.88 689.20 844.09 698.70 839.06 707.45C838.67 707.73 837.89 708.28 837.50 708.56C837.44 709.14 837.40 709.74 837.37 710.34C835.07 712.87 833.94 716.19 831.74 718.81C824.09 731.53 815.77 744.15 805.40 754.80C805.45 755.24 805.52 755.70 805.60 756.17C777.75 787.70 747.64 817.65 712.09 840.48C711.38 840.41 709.97 840.26 709.26 840.19C709.28 840.80 709.31 842.01 709.33 842.61C699.50 848.37 689.72 854.37 680.09 860.42C679.55 860.40 678.45 860.36 677.90 860.35C670.19 866.31 660.48 869.19 652.06 874.08C647.03 875.15 642.61 878.00 637.99 880.13C635.87 880.44 633.86 881.19 632.08 882.37C631.24 882.42 630.41 882.46 629.60 882.53C628.12 883.66 626.40 884.35 624.59 884.70C599.85 893.70 574.16 900.19 548.01 903.20C522.66 907.54 496.73 906.55 471.01 906.35C454.72 904.14 437.42 904.99 422.11 898.78C391.07 888.95 373.10 850.19 387.18 820.43C393.15 809.49 399.39 798.65 406.59 788.48C406.62 787.94 406.67 786.87 406.70 786.34C410.53 783.70 411.61 778.98 414.31 775.43C427.44 755.40 439.43 734.65 452.41 714.53C452.46 713.80 452.50 713.09 452.56 712.38C452.97 712.12 453.80 711.60 454.21 711.33C472.15 683.24 489.84 655.01 507.26 626.60C520.15 607.25 531.77 587.11 544.74 567.80C555.72 550.18 566.32 532.29 578.64 515.52C579.09 515.32 580.00 514.92 580.45 514.72C584.86 507.49 593.40 504.81 599.80 499.80C605.84 498.60 611.46 495.59 617.74 495.36C628.47 493.31 639.29 496.12 649.33 499.83C660.30 505.72 670.53 513.51 676.86 524.43C678.03 527.23 679.46 529.92 680.98 532.56C685.65 545.74 688.35 560.60 683.19 574.08C678.89 590.04 668.09 602.91 659.82 616.86C655.00 624.41 650.41 632.07 645.54 639.59C645.45 640.08 645.27 641.07 645.18 641.57C634.30 655.91 625.78 672.04 615.57 686.85C615.46 687.29 615.25 688.15 615.14 688.58C608.62 697.93 602.94 707.85 596.73 717.42C591.71 725.18 586.57 732.88 581.93 740.89C576.13 749.30 571.17 758.27 565.88 766.98C563.15 771.90 559.32 776.15 557.04 781.32C555.28 783.08 553.89 785.14 553.78 787.72C572.98 786.47 590.65 777.75 608.46 771.27C614.88 767.87 621.81 765.44 628.02 761.66C628.54 761.65 629.60 761.65 630.13 761.64C632.92 758.66 637.16 757.66 640.56 755.49C673.60 736.67 702.26 710.52 725.23 680.27C734.43 666.30 743.99 652.55 751.52 637.59C758.91 622.31 765.12 606.43 769.93 590.16C771.05 586.60 773.17 583.15 772.27 579.28C772.64 579.38 773.36 579.58 773.73 579.68C777.99 558.01 782.01 536.17 782.67 513.97C783.11 471.69 774.68 429.46 758.35 390.52C750.91 376.57 744.98 361.49 734.63 349.36C734.55 348.70 734.48 348.04 734.43 347.39C718.55 325.78 701.09 304.46 679.21 288.73C678.96 288.30 678.46 287.45 678.21 287.02C671.80 283.09 666.48 277.40 659.73 274.05C653.16 268.58 645.27 265.21 638.15 260.64C637.61 260.56 637.09 260.48 636.57 260.40C635.93 259.72 635.30 259.05 634.67 258.38C634.14 258.33 633.09 258.24 632.56 258.19C627.30 254.63 621.47 252.03 615.49 249.92C612.62 248.18 609.52 246.83 606.24 246.10C605.85 245.73 605.08 244.97 604.69 244.59C580.80 236.31 556.25 228.96 531.01 226.43C511.55 222.88 491.66 225.01 472.00 224.48C444.69 227.43 417.57 232.02 391.98 242.38C391.45 242.37 390.39 242.35 389.86 242.33C382.63 247.00 373.28 249.09 366.03 254.31C365.40 254.32 364.15 254.34 363.52 254.35C349.56 263.79 334.18 271.27 321.34 282.40C317.70 285.43 313.96 288.35 310.01 290.97C304.99 297.83 297.06 302.00 292.33 309.24C273.73 328.05 257.96 349.51 244.98 372.50C241.77 376.97 240.36 382.58 237.46 386.95C237.45 387.45 237.44 388.45 237.43 388.95C232.09 396.08 229.76 405.82 222.00 411.02C217.76 416.27 211.32 418.66 205.93 422.42C204.78 422.42 203.63 422.42 202.48 422.42C201.69 423.05 200.91 423.69 200.12 424.32C186.45 426.32 171.54 425.89 159.41 418.55C158.89 418.47 157.85 418.32 157.33 418.24C153.36 413.60 147.41 411.16 143.93 405.99C139.30 402.03 137.80 395.97 134.47 391.09C134.53 390.42 134.65 389.09 134.71 388.43C134.03 387.32 133.36 386.22 132.68 385.13C132.55 378.41 130.44 371.73 131.82 365.03C132.27 354.99 137.20 346.03 140.98 336.95C142.87 332.66 145.79 328.89 147.17 324.37C148.85 322.33 150.29 320.06 151.01 317.49C179.32 267.71 217.68 223.31 263.89 189.22C269.02 185.44 274.69 182.33 279.28 177.84C279.83 177.75 280.92 177.56 281.46 177.47C299.89 165.06 319.98 155.44 340.02 145.92C342.86 145.14 345.67 144.22 348.47 143.28C376.55 131.32 406.73 124.53 436.95 120.74Z"/>
                <path d="M432.32 344.48C457.72 340.53 485.03 356.16 493.22 380.77C495.25 389.30 495.94 398.07 496.62 406.79C495.86 422.36 485.73 435.09 477.76 447.76C412.01 551.93 345.12 655.36 279.72 759.74C272.04 773.31 259.17 784.20 243.82 787.80C214.41 797.09 179.90 777.16 172.84 747.17C169.95 732.97 171.27 717.52 178.75 704.83C219.31 640.18 261.36 576.47 301.65 511.65C331.78 465.03 361.02 417.86 391.28 371.33C400.29 356.94 415.50 346.94 432.32 344.48Z"/>
            </symbol>
        `);

        this.addDock({
            type: "SiyuanMediaSidebar",
            config: { position: "RightTop", size: { width: 400, height: 480 }, icon: iconId, title: this.i18n.sidebar?.title || "媒体播放器" },
            data: { plugin: this },
            init() {
                const plugin = this.data.plugin as MediaPlayerPlugin;
                this.element.classList.add('media-player-sidebar');
                const contentEl = document.createElement('div');
                contentEl.className = 'media-player-sidebar-content';
                this.element.appendChild(contentEl);
                plugin.showTabContent('playlist', contentEl);
            },
            resize() {},
            destroy() {
                const plugin = this.data.plugin as MediaPlayerPlugin;
                plugin.components.forEach(c => c?.$destroy?.());
                plugin.components.clear();
            }
        });

        this.addTopBar({
            icon: `<svg viewBox="0 0 1024 1024"><path fill="#8b5cf6" d="M753.265 105.112c12.57 12.546 12.696 32.81 0.377 45.512l-0.377 0.383-73.131 72.992L816 224c70.692 0 128 57.308 128 128v448c0 70.692-57.308 128-128 128H208c-70.692 0-128-57.308-128-128V352c0-70.692 57.308-128 128-128l136.078-0.001-73.13-72.992c-12.698-12.674-12.698-33.222 0-45.895 12.697-12.674 33.284-12.674 45.982 0l119.113 118.887h152.126l119.114-118.887c12.697-12.674 33.284-12.674 45.982 0zM457 440c-28.079 0-51 22.938-51 51v170c0 9.107 2.556 18.277 7 26 15.025 24.487 46.501 32.241 71 18l138-84c7.244-4.512 13.094-10.313 17-17 15.213-24.307 7.75-55.875-16-71l-139-85c-7.994-5.355-17.305-8-27-8z"/></svg>`,
            title: this.i18n.name || '媒体播放器',
            position: 'right',
            callback: (e: MouseEvent) => {
                const menu = new Menu();
                menu.addItem({ icon: 'iconSettings', label: this.i18n.settings?.title || '设置', click: () => this.openSettings() });
                menu.open({ x: e.clientX, y: e.clientY });
            }
        });
    }

    /** 打开设置面板 */
    private openSettings() {
        document.querySelector('.dock__item[aria-label*="媒体播放器"]')?.dispatchEvent(new MouseEvent('click', { bubbles: true }));
        setTimeout(() => this.showTabContent('settings', document.querySelector('.media-player-sidebar-content') as HTMLElement), 100);
    }

    /** 显示标签页内容，创建组件实例 */
    private async showTabContent(tabId: string, container: HTMLElement) {
        const components = { playlist: PlayList, assistant: Assistant, notes: Notes, settings: Setting };

        container.querySelectorAll('[data-tab-id]').forEach(el => el.classList.toggle('fn__none', el.getAttribute('data-tab-id') !== tabId));

        if (this.components.has(tabId)) {
            window.dispatchEvent(new CustomEvent('mediaPlayerTabActivate', { detail: { tabId } }));
            return;
        }

        const el = document.createElement('div');
        el.setAttribute('data-tab-id', tabId);
        container.appendChild(el);

        const component = components[tabId];
        if (!component) return;

        const baseProps = { config: await this.getConfig(), i18n: this.i18n, activeTabId: tabId, api: this.playerAPI, plugin: this };
        const specificProps = {
            playlist: { currentItem: this.playerAPI?.getCurrentMedia?.() },
            settings: { group: 'media-player' },
            assistant: {
                player: this.playerAPI,
                currentMedia: this.playerAPI?.getCurrentMedia?.(),
                insertContentCallback: async (content: any) => {
                    const { doc } = await import('./core/document');
                    doc.insert(content, await this.getConfig(), this.i18n);
                },
                createTimestampLinkCallback: this.playerAPI.createTimestampLink
            },
            notes: {}
        };

        const instance = new component({ target: el, props: { ...baseProps, ...specificProps[tabId] } });
        this.components.set(tabId, instance);

        if (tabId === 'playlist') {
            instance.$on?.('play', async (event: CustomEvent<any>) => {
                const config = await this.getConfig();
                const isBuiltIn = config.settings.playerType === 'built-in';
                if (isBuiltIn) { this.openTab(); setTimeout(() => window.dispatchEvent(new CustomEvent('playMediaItem', { detail: event.detail })), 300); }
                else window.dispatchEvent(new CustomEvent('playMediaItem', { detail: event.detail }));
            });
        } else if (tabId === 'settings') {
            instance.$on?.('changed', (event: CustomEvent<any>) => {
                window.dispatchEvent(new CustomEvent('updatePlayerConfig', { detail: event.detail.settings }));
            });
        }

        window.dispatchEvent(new CustomEvent('mediaPlayerTabActivate', { detail: { tabId } }));
    }

    /** 打开播放器标签页 */
    private async openTab() {
        const config = await this.getConfig();
        const plugin = this;

        this.addTab({
            type: "custom_tab",
            init() {
                const container = document.createElement("div");
                container.className = "media-player-container";

                const playerArea = document.createElement("div");
                playerArea.className = "player-area";
                // 移动端适配：调整播放器区域样式
                const isMobile = plugin.isMobile();
                playerArea.style.cssText = `width:100%;height:${isMobile ? '60vh' : '100%'};position:relative;z-index:1;${isMobile ? 'min-height:300px;' : ''}`;

                const mediaPlayerTab = document.createElement("div");
                mediaPlayerTab.className = "media-player-tab";
                mediaPlayerTab.appendChild(document.createElement("div")).className = "content-area";
                mediaPlayerTab.querySelector('.content-area').appendChild(playerArea);
                container.appendChild(mediaPlayerTab);

                plugin.tabInstance = this;
                plugin.components.set('player', new Player({
                    target: playerArea,
                    props: { config: config.settings, i18n: plugin.i18n, currentItem: plugin.playerAPI?.getCurrentMedia?.(), api: plugin.playerAPI }
                }));

                (window as any).siyuanMediaPlayer = plugin.playerAPI;
                window.dispatchEvent(new CustomEvent('siyuanMediaPlayerUpdate', {
                    detail: { player: plugin.playerAPI, currentItem: plugin.playerAPI?.getCurrentMedia?.() }
                }));

                this.element.appendChild(container);
            },
            destroy() {
                const player = plugin.components.get('player');
                if (player?.$destroy) {
                    try { player.$destroy(); } catch (e) {}
                    plugin.components.delete('player');
                }
                plugin.tabInstance = null;
                (window as any).siyuanMediaPlayer = null;
            }
        });

        const openMode = config?.settings?.openMode || 'default';
        const tabOptions: any = {
            app: this.app,
            custom: { icon: "siyuan-media-player-icon", title: this.i18n.name, id: this.name + "custom_tab" }
        };

        // 移动端适配：移动端不支持position参数
        if (!this.isMobile()) {
            if (openMode === 'right') tabOptions.position = 'right';
            else if (openMode === 'bottom') tabOptions.position = 'bottom';
        }
        // 画中画模式：正常打开标签页，播放器会自动启用画中画

        openTab(tabOptions);
    }

    /** 添加快捷键命令 */
    private addHotkeys() {
        const actions = ['screenshot', 'timestamp', 'loopSegment', 'mediaNotes', 'openSidebar'];
        const texts = [this.i18n.screenshot?.text, this.i18n.timestamp?.text, this.i18n.loopSegment?.text, this.i18n.controlBar?.mediaNotes?.name, "打开媒体播放器面板"];

        actions.forEach((action, i) => this.addCommand({
            langKey: action,
            langText: texts[i] || action,
            hotkey: "",
            callback: () => {
                if (action === 'openSidebar') return document.querySelector('.dock__item[aria-label*="媒体播放器"]')?.dispatchEvent(new MouseEvent('click', { bubbles: true }));

                if (!this.playerAPI?.getCurrentMedia?.()) {
                    showMessage(this.i18n.openPlayer);
                    this.openTab();
                    return;
                }

                this.playerAPI.triggerAction(action);
            }
        }));
    }
}